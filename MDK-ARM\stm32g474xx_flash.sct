; *********************************************************************************
; *** Scatter-Loading Description File generated by STM32CubeMX/LinkerGenerator ***
; *********************************************************************************;

LR_IROM1 0x8000000 0x00080000  {  ; load_region size_region
  ER_IROM1 0x8000000 0x00080000  {  ; load address = excetion address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+XO)
   .ANY (+RO)
  }
  RW_IRAM1 0x20000000 0x00020000  {  ; load address = excetion address
   .ANY (+RW +ZI)
  }
;***region***;
;***endregion***;
}


/*-Start of  regions- Auto-generated By STM32CubeMX-*/
