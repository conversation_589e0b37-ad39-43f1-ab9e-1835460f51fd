#include "Encoder.h"
#include "main.h"
#include "gpio.h"

static int16_t Count = 0; // 编码器计数值
static uint8_t encoder_state = 0; // 编码器状态标志
static uint32_t last_interrupt_time = 0; // 上次中断时间，用于消抖

void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin) // GPIO中断回调函数
{
    uint32_t current_time = HAL_GetTick();

    // 消抖处理：如果距离上次中断时间小于5ms，则忽略
    if(current_time - last_interrupt_time < 5) return;
    last_interrupt_time = current_time;

    uint8_t pin3_state = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_3);
    uint8_t pin4_state = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_4);

    if(GPIO_Pin == GPIO_PIN_3) // PB3中断
    {
        if(pin3_state == GPIO_PIN_RESET) // 下降沿
        {
            if(pin4_state == GPIO_PIN_SET) Count++; // 顺时针
            else Count--; // 逆时针
        }
    }
    else if(GPIO_Pin == GPIO_PIN_4) // PB4中断
    {
        if(pin4_state == GPIO_PIN_RESET) // 下降沿
        {
            if(pin3_state == GPIO_PIN_SET) Count--; // 逆时针
            else Count++; // 顺时针
        }
    }

    encoder_state = 1; // 设置编码器状态标志
}

void Encoder_Task(void) // 编码器任务函数
{
    encoder_state = 0; // 清除编码器状态标志
}

int16_t GetCount(void)
{
    return Count;
}
