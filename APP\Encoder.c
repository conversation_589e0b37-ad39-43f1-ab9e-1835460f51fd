#include "Encoder.h"
#include "main.h"
#include "gpio.h"

int16_t Count = 0;

void encoder_task(void)
{
	if(__HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_3))
	{
		if(HAL_GPIO_ReadPin(GPIOB,GPIO_PIN_4) == GPIO_PIN_RESET)
		{
		  Count--;
		}
	}
	if(__HAL_GPIO_EXTI_CLEAR_FLAG(GPIO_PIN_4))
	{
		if(HAL_GPIO_ReadPin(GPIOB,GPIO_PIN_3) == GPIO_PIN_RESET)
		{
		  Count++;
		}
	}
}

int16_t GetCount(void)
{
	return Count;
}
