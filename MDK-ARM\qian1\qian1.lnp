--cpu=Cortex-M4.fp.sp
"qian1\startup_stm32g474xx.o"
"qian1\main.o"
"qian1\gpio.o"
"qian1\i2c.o"
"qian1\stm32g4xx_it.o"
"qian1\stm32g4xx_hal_msp.o"
"qian1\stm32g4xx_hal_i2c.o"
"qian1\stm32g4xx_hal_i2c_ex.o"
"qian1\stm32g4xx_hal.o"
"qian1\stm32g4xx_hal_rcc.o"
"qian1\stm32g4xx_hal_rcc_ex.o"
"qian1\stm32g4xx_hal_flash.o"
"qian1\stm32g4xx_hal_flash_ex.o"
"qian1\stm32g4xx_hal_flash_ramfunc.o"
"qian1\stm32g4xx_hal_gpio.o"
"qian1\stm32g4xx_hal_exti.o"
"qian1\stm32g4xx_hal_dma.o"
"qian1\stm32g4xx_hal_dma_ex.o"
"qian1\stm32g4xx_hal_pwr.o"
"qian1\stm32g4xx_hal_pwr_ex.o"
"qian1\stm32g4xx_hal_cortex.o"
"qian1\system_stm32g4xx.o"
"qian1\key_app.o"
"qian1\oled_app.o"
"qian1\scheduler.o"
"qian1\encoder.o"
"qian1\oled.o"
--strict --scatter "stm32g474xx_flash.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "qian1.map" -o qian1\qian1.axf