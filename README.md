# STM32G474 OLED项目

## 项目概述
基于STM32G474的OLED显示项目，使用0.91寸SSD1306 OLED屏幕通过I2C接口进行通信。

## 硬件配置
- MCU: STM32G474
- OLED: 0.91寸 SSD1306 (128x32分辨率)
- I2C接口: I2C1
  - SCL: PB8
  - SDA: PB9
- I2C地址: 0x78
- 旋转编码器:
  - A相: PB3 (外部中断，下降沿触发)
  - B相: PB4 (外部中断，下降沿触发)

## 软件架构
项目采用分层架构设计：

### Hardware层 (Hardware/oled/)
- `oled.c/oled.h`: OLED硬件驱动层
- `oledfont.h`: 字体数据
- `oledpic.h`: 图片数据

### Application层 (APP/)
- `oled_app.c/oled_app.h`: OLED应用逻辑层
- `scheduler.c/scheduler.h`: 任务调度器
- `key_app.c/key_app.h`: 按键应用层
- `Encoder.c/Encoder.h`: 旋转编码器应用层
- `mydefine.h`: 统一配置文件

## OLED应用层API

### 初始化函数
```c
void OLED_App_Init(void);
```
- 功能: 初始化OLED硬件并清屏
- 调用时机: 系统启动后，I2C初始化完成后

### 显示函数
```c
void OLED_App_ShowHello(void);
```
- 功能: 在OLED第一行显示"hello"
- 字体: 16号字体
- 位置: 坐标(0,0)

### 任务调度函数
```c
void OLED_task(void);
```
- 功能: 任务调度器调用的OLED任务
- 周期: 100ms
- 用途: 显示编码器计数值

## 旋转编码器API

### 初始化函数
```c
void Encoder_Init(void);
```
- 功能: 初始化旋转编码器
- 调用时机: 系统启动后，GPIO初始化完成后

### 获取计数值
```c
int32_t Encoder_GetCount(void);
```
- 功能: 获取编码器当前计数值
- 返回值: 编码器计数值（正数表示顺时针，负数表示逆时针）

### 重置计数值
```c
void Encoder_ResetCount(void);
```
- 功能: 重置编码器计数值为0

### 获取状态
```c
encoder_state_t Encoder_GetState(void);
```
- 功能: 获取编码器当前状态
- 返回值: ENCODER_IDLE(空闲)、ENCODER_CW(顺时针)、ENCODER_CCW(逆时针)

### 任务调度函数
```c
void Encoder_Task(void);
```
- 功能: 编码器任务函数
- 周期: 10ms
- 用途: 清除编码器状态标志

## 使用方法

1. 在main.c中包含头文件：
```c
#include "oled_app.h"
```

2. 在初始化部分调用：
```c
OLED_App_Init();        // 初始化OLED
OLED_App_ShowHello();   // 显示hello
Encoder_Init();         // 初始化旋转编码器
scheduler_init();       // 初始化任务调度器
```

3. 在主循环中运行任务调度器：
```c
while (1) {
    scheduler_run();    // 运行任务调度器
}
```

## 任务调度器
项目使用基于时间片的任务调度器，支持多任务并发执行：

### 当前任务配置
- **按键任务**: 1ms周期，处理按键扫描
- **OLED任务**: 100ms周期，处理OLED显示更新

### 添加新任务
在`scheduler.c`的`scheduler_task[]`数组中添加：
```c
{task_function, period_ms, 0}
```

## 编译说明
- 开发环境: Keil MDK-ARM
- 项目文件: MDK-ARM/qian1.uvprojx
- 编译器: ARM Compiler 5

## 注意事项
- 确保I2C1正确初始化
- OLED电源连接正确
- I2C上拉电阻配置正确（通常内部上拉即可）

## 代码规范修复
- 修复了函数声明中缺少参数类型的警告
- 所有无参数函数现在正确使用 `void` 声明
- 添加了函数注释说明
- 修复了OLED_task函数的声明问题
- 正确配置了任务调度器
